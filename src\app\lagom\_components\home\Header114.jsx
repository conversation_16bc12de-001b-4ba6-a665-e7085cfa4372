"use client";

import { But<PERSON> } from "@relume_io/relume-ui";
import React from "react";

export function Header114() {
  return (
    <section id="relume" className="relative px-[5%]">
      <div className="container flex max-h-[100rem] min-h-[120vh]">
        <div className="py-20 md:py-32 lg:py-40">
          <div className="relative z-10 grid h-full auto-cols-fr grid-cols-1 gap-40 py-20">
            <div className="flex flex-col justify-start md:justify-center pl-[5%] pr-[20%] mt-20">
              <h1 className="text-6xl font-semibold text-text-alternative md:text-9xl lg:text-10xl ">
                Walk the talk, zeggen ze.<br></br> Wij doen het.
              </h1>
              <div className="mt-6 flex flex-wrap gap-4 md:mt-8">
                <Button title="Button" className="border-none bg-background-primary text-text-primary rounded-lg hover:bg-background-secondary">Ontdek meer</Button>
                <Button 
                  title="Button" 
                  variant="secondary"
                  className="lg:border-opacity-50 border border-border-primary bg-transparent text-text-alternative rounded-lg hover:bg-background-primary/50"
                >
                  Contacteer ons
                </Button>
              </div>
            </div>
            <div className="pl-[40%] pr-[5%] mt-30 flex flex-col justify-end">
              <h1 className="text-4xl font-semibold text-text-alternative md:text-5xl lg:text-6xl mb-6">
                Medium length hero heading goes here
              </h1>
              <p className="text-text-alternative md:text-md ">
                Klinkt mooi, maar we dóen het ook. Samen met jullie, bedrijven
                groot en klein. En dan hebben we het over duurzaamheid in al z’n
                vormen. Over impact maken. Op alle mogelijke manieren. Dat is de
                meerwaarde van ons ecosysteem. We rollen de mouwen op om samen
                lokaal natuur te creëren en jouw ESG-verhaal tastbaar te maken.
                Om jouw bedrijf een stem te geven, zodat je anderen kan
                inspireren. En om de betrokkenheid van jouw stakeholders te
                vergroten door ze slim én leuk te verbinden. Zorgen voor
                daadkracht. En draagvlak creëren. Inspireren en verbinden. Dat
                is wat we doen!
              </p>
            </div>
          </div>
        </div>
      </div>
      <div className="absolute inset-0 z-0">
        <img
          src="/images/forestforward/homepage/9.png"
          className="size-full object-cover"
          alt="Relume placeholder image"
        />
        <div className="absolute inset-0 bg-black/75" />
      </div>
    </section>
  );
}
