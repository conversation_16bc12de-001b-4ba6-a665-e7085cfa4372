.theme-storyforward {
  /* Background Colors */
  --color-background: #ffffff;
  --color-background-primary: #fef9f5;         /* Bay of Many Lightest */
  --color-background-secondary: #fbece1;       /* Bay of Many Lighter */
  --color-background-tertiary: #f5cdae;        /* Bay of Many Light */
  --color-background-alternative: #e78235;     /* Bay of Many */

  /* Border Colors */
  --color-border: #d9d9d9;
  --color-border-primary: #d9d9d9;
  --color-border-secondary: #d9d9d9;
  --color-border-tertiary: #d9d9d9;      
  --color-border-alternative: rgba(91, 47, 18, 0.15); /* Neutral Darkest 15% equivalent */

  /* Text Colors */
  --color-text: #000000;                       /* Bay of Many Darkest */
  --color-text-primary: #000000;               /* Darker */
  --color-text-secondary: #000000;             /* Dark */
  --color-text-alternative: #ffffff;

  /* Accent & Link Colors */
  --color-link: #e78235;                       /* Accent/Base */
  --color-link-primary: #b66327;               /* Dark */
  --color-link-secondary: #8a4a1c;             /* Darker */
  --color-link-alternative: #f0b680;           /* Light */

  --color-accent: #e78235;                     /* Used for buttons, highlights, etc. */
}
